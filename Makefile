CONTAINER_NAME=chatbot-base-frankenphp

start:
	docker compose up --build -d --remove-orphans

stop:
	docker compose stop

down:
	docker compose down

rebuild:
	docker compose build --pull --force-rm --no-cache
	make start

## Tests
test:
	docker exec -e APP_ENV=test -it ${CONTAINER_NAME} php -d memory_limit=512M vendor/bin/phpunit --configuration phpunit.dist.xml

# Composer
composer-install:
	docker exec -it ${CONTAINER_NAME}  composer install

composer-update:
	docker exec -it ${CONTAINER_NAME}  composer update

cache-clear:
	docker exec -it ${CONTAINER_NAME} php bin/console cache:clear

## code
fix-code:
	docker exec -it ${CONTAINER_NAME} php -d memory_limit=512M vendor/bin/php-cs-fixer --verbose --allow-risky=yes --config=.php-cs-fixer.dist.php fix

analyse-code:
	docker exec -it ${CONTAINER_NAME} php -d memory_limit=512M vendor/bin/phpstan analyse -c ./phpstan.dist.neon src tests
