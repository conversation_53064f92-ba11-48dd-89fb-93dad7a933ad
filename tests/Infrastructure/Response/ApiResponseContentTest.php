<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Response;

use App\Infrastructure\Response\ApiResponseContent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ApiResponseContentTest extends TestCase
{
    /**
     * @param array<int, mixed>|array<string, mixed> $expectedArray
     */
    #[DataProvider('toArrayDataProvider')]
    public function testToArray(ApiResponseContent $responseContent, array $expectedArray): void
    {
        $this->assertEquals($expectedArray, $responseContent->toArray());
    }

    public static function toArrayDataProvider(): \Generator
    {
        yield 'data not defined' => [
            'responseContent' => new ApiResponseContent(),
            'expectedArray' => [],
        ];

        yield 'empty data' => [
            'responseContent' => ApiResponseContent::createFromData([]),
            'expectedArray' => [
                'data' => [],
            ],
        ];

        yield 'with data' => [
            'responseContent' => ApiResponseContent::createFromData(['data' => 'data']),
            'expectedArray' => ['data' => [
                'data' => 'data',
            ]],
        ];

        yield 'with message' => [
            'responseContent' => ApiResponseContent::createFromMessage('message'),
            'expectedArray' => ['message' => 'message', 'error' => 1],
        ];

        yield 'with metadata' => [
            'responseContent' => new ApiResponseContent(metadata: ['metadata1' => 'metadata1'])->addMetadata(['metadata' => 'metadata']),
            'expectedArray' => ['metadata' => [
                'metadata1' => 'metadata1',
                'metadata' => 'metadata',
            ]],
        ];

        yield 'complete' => [
            'responseContent' => ApiResponseContent::createFromData(['data' => 'data'])
                ->setMetadata(['metadata' => 'metadata']),
            'expectedArray' => [
                'data' => [
                    'data' => 'data',
                ],
                'metadata' => [
                    'metadata' => 'metadata',
                ],
            ],
        ];
    }
}
