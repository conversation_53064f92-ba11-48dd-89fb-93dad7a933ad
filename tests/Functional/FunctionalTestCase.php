<?php

declare(strict_types=1);

namespace App\Tests\Functional;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class FunctionalTestCase extends WebTestCase
{
    protected ?KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        try {
            $this->client = self::createClient();
        } catch (\Exception $e) {
            $this->fail('You cannot create the client used in functional tests. Check the error: ' . $e->getMessage());
        }

        $this->client->disableReboot();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->client = null;

        restore_exception_handler();
    }

    /**
     * @param string|array<mixed, mixed> $body
     * @param array<string, mixed>        $queryParams
     * @param array<string, mixed>        $headers
     */
    protected function makeRequest(
        string $method,
        string $uri,
        string|array $body = [],
        array $queryParams = [],
        array $headers = [],
    ): Response {
        // Uncomment when authentication is implemented
        //        $headers = array_merge(
        //            $loggedUserId ? ['HTTP_Authorization' => 'Bearer ' . $loggedUserId->value()] : [],
        //            $headers
        //        );
        if (null === $this->client) {
            $this->fail('Cannot make request');
        }

        $body = \is_array($body) ? json_encode($body) : $body;
        if (false === $body) {
            $this->fail('Cannot make request');
        }

        $this->client->request(
            method: $method,
            uri: 'http://localhost' . $uri,
            parameters: $queryParams,
            files: [],
            server: $headers,
            content: $body,
        );

        return $this->client->getResponse();
    }
}
