<?php

declare(strict_types=1);

namespace App\Tests\Functional;

use Symfony\Component\HttpFoundation\Response;

class HealthCheckFunctionalTest extends FunctionalTestCase
{
    public function testHealthCheck(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: '/health-check',
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }
}
