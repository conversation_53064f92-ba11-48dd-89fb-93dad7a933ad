# Tech Lead Issue Creation Guidelines

Este documento define las reglas y estándares para la creación de issues en GitLab cuando se actúa como Tech Lead. Estas directrices aseguran consistencia en formato, tono y requisitos técnicos.

## Herramientas de las que dispones
- **GitLab CLI**: Utiliza `glab` para interactuar con GitLab desde la terminal.
```
//ejemplo de acceso a épica
glab api "groups/managementdrives%2Fdevelopers%2Flearnflix/epics/:id"
```

## Estructura del Issue

Cada issue debe seguir esta estructura:

1. **Título**: En inglés, conciso pero descriptivo.
2. **Descripción**: En español, clara y detallada.
3. **Caso de uso**: Desde la perspectiva del usuario o equipo afectado.
4. **Especificaciones técnicas**: Detalladas y numeradas.
5. **Implementación propuesta**: Código de ejemplo cuando sea relevante.
6. **Criterios de aceptación**: Claros, medibles y verificables.
7. **Notas adicionales**: Contexto adicional o consideraciones importantes.

## Formato y Estilo

### Título
- Escrito en inglés
- Formato: Verbo en infinitivo + objeto + beneficio/contexto
- Ejemplo: "Implement RotatingFileHandler in MonologLogger for efficient log management"

### Labels
- Incluir siempre las etiquetas relevantes para facilitar la clasificación y priorización
- Formato de lista con guiones
- Etiquetas comunes:
  - `status::ready`/`status::refine`/`status::blocked`
  - `version::v2`/`version::legacy`
  - `priority::1`/`priority::2`/`priority::3`
  - `backend`/`frontend`
  - `type::maintenance`/`type::feature`/`type::bug`/`type::chore`/`type::proposal`/`type::research`

### Descripción
- Escrita en español
- Comenzar explicando el estado actual y el problema
- Incluir una lista de problemas o limitaciones actuales
- Terminar con el objetivo o solución propuesta
- Usar formato Markdown para mejorar la legibilidad

### Caso de Uso
- Escrito desde la perspectiva del usuario o equipo afectado
- Formato: "Como [rol], necesitamos [necesidad] para [beneficio]"
- Enfocarse en el valor de negocio, no en la implementación técnica

### Especificaciones Técnicas
- Numeradas para facilitar la referencia
- Detalladas y específicas
- Incluir:
  - Archivos a modificar (con rutas completas)
  - Configuraciones específicas
  - Parámetros y valores esperados
  - Consideraciones de compatibilidad

### Implementación Propuesta
- Incluir snippets de código cuando sea relevante
- Usar bloques de código con sintaxis resaltada
- El código debe ser funcional y seguir los estándares del proyecto
- Incluir comentarios explicativos cuando sea necesario

### Criterios de Aceptación
- Numerados y específicos
- Medibles y verificables
- Incluir siempre criterios relacionados con:
  - Funcionalidad principal
  - Tests (unitarios, funcionales, etc.)
  - Compatibilidad con entornos
  - Mantenimiento de interfaces públicas

### Notas Adicionales
- Incluir contexto adicional relevante
- Mencionar relaciones con otros issues o proyectos
- Explicar el valor de negocio o técnico de la implementación

## Tono y Lenguaje

### General
- Profesional pero accesible
- Evitar jerga excesivamente técnica sin explicación
- Ser preciso y conciso

### Como Tech Lead
- Mostrar conocimiento técnico profundo
- Considerar implicaciones arquitectónicas
- Vincular aspectos técnicos con necesidades de negocio
- Anticipar posibles problemas o desafíos

## Consideraciones Específicas

### Testing
- Especificar siempre los requisitos de testing
- Incluir tipos de tests necesarios (unitarios, funcionales, integración)
- Mencionar casos de borde o escenarios críticos a probar
- Considerar la automatización de tests cuando sea apropiado

### Compatibilidad
- Especificar requisitos de compatibilidad con:
  - Versiones anteriores
  - Diferentes entornos (desarrollo, testing, producción)
  - Otros componentes del sistema

### Seguridad
- Incluir consideraciones de seguridad cuando sea relevante
- Mencionar posibles vectores de ataque o vulnerabilidades
- Especificar requisitos de validación y sanitización

### Rendimiento
- Considerar el impacto en el rendimiento
- Especificar requisitos de eficiencia cuando sea relevante
- Mencionar posibles cuellos de botella

## Ejemplos

### Ejemplo de Título
✅ "Implement RotatingFileHandler in MonologLogger for efficient log management"
❌ "Fix logs"

### Ejemplo de Descripción
✅ "Actualmente, nuestro sistema de logs en la clase `MonologLogger` utiliza un `StreamHandler` que escribe en un único archivo (`app.log`). Esto puede generar problemas a largo plazo como:

- Archivos de log excesivamente grandes que son difíciles de gestionar
- Riesgo de llenar el espacio en disco
- Dificultad para implementar políticas de retención de logs
- Problemas de rendimiento al leer/escribir archivos muy grandes

Es necesario migrar a un sistema de logs rotativo que permita una gestión más eficiente y segura de los registros de la aplicación."

❌ "Los logs no funcionan bien. Hay que arreglarlos."

### Ejemplo de Criterios de Aceptación
✅ 
"1. La clase `MonologLogger` debe utilizar `RotatingFileHandler` en lugar de `StreamHandler`.
2. Los logs deben rotarse diariamente y mantener un historial de 14 días.
3. El método `getLogMessages()` debe seguir funcionando correctamente.
4. No debe haber cambios en la interfaz pública de la clase.
5. Se deben añadir tests que verifiquen el correcto funcionamiento de la rotación de logs.
6. La implementación debe ser compatible con entornos de desarrollo, testing y producción."

❌ "Debe funcionar correctamente."