FROM registry.gitlab.com/managementdrives/cloud-devops/base-images/public/base-image-frankenphp:8.4

ARG user=frankenphp
ENV APP_ENV=dev

RUN mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"

RUN set -eux; \
  install-php-extensions xdebug; \
  chown -R frankenphp:frankenphp /app ; \
  git config --global --add safe.directory /app \
  ;


COPY --link --chmod=755 docker/dev/frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint

USER ${USER}

ENTRYPOINT ["docker-entrypoint"]

CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]
