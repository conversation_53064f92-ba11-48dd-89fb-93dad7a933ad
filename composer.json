{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*", "league/tactician-bundle": ">=1.5.2", "monolog/monolog": ">=3.9", "runtime/frankenphp-symfony": "^0.2.0", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2.8.1", "symfony/framework-bundle": "7.3.*", "symfony/runtime": "7.3.*", "symfony/validator": "7.3.*", "symfony/yaml": "7.3.*"}, "require-dev": {"brainmaestro/composer-git-hooks": "^3.0", "friendsofphp/php-cs-fixer": "^3.85.1", "phpstan/phpstan": "^2.1.22", "phpunit/phpunit": "^11.5.28", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/phpunit-bridge": "^7.3.1"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "fix-code:project": "@php vendor/bin/php-cs-fixer fix --allow-risky=yes --config=.php-cs-fixer.dist.php", "fix-code:staged": "git status -s | grep -e '^[ AM]' | cut -c4- | tr \"\\n\" \" \" | xargs -r vendor/bin/php-cs-fixer --verbose --config=.php-cs-fixer.dist.php fix --allow-risky=yes", "cghooks": "vendor/bin/cghooks", "phpstan": "phpstan analyse -l 5 src tests --memory-limit=512M"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}, "hooks": {"pre-commit": "git status -s | grep -e '^[ AM]' | cut -c4- | tr \"\\n\" \" \" | xargs -r vendor/bin/php-cs-fixer --verbose --config=.php-cs-fixer.dist.php fix --allow-risky=yes"}}}