include:
    - project: ManagementDrives/cloud-devops/ci-cd-templates
      file: /templates/docker/docker.yml
      ref: 1.2.0
    - project: ManagementDrives/cloud-devops/ci-cd-templates
      file: /templates/terraform/terraform.yml
      ref: 1.2.0


stages:
    - commit

workflow:
    rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
          variables:
              DOCKER_TAG: mr-$CI_MERGE_REQUEST_IID-$CI_COMMIT_SHORT_SHA
        - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
          when: never
        - if: $CI_COMMIT_BRANCH

# templates
.php_cli:
    image: registry.gitlab.com/managementdrives/cloud-devops/base-images/public/base-image-frankenphp:8.4
    before_script:
        - composer install
    cache:
        policy: pull
        key:
            files:
                - composer.lock
                - symfony.lock
        paths:
            - vendor/

### .pre stage. If new vendors needs to be added
composer-cache:
    stage: .pre
    extends: .php_cli
    script:
        - composer outdated
    cache:
        policy: pull-push
    rules:
        - changes:
              - composer.lock
              - symfony.lock

### Commit stage
#### Linting
lint-dockerfile:
    stage: commit
    extends: .hadolint_dockerfile
    rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
          changes:
              - Dockerfile

lint-php:
    stage: commit
    extends: .php_cli
    script:
        - php -d memory_limit=512M vendor/bin/phpstan analyse -c ./phpstan.dist.neon
    rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    allow_failure: true

#### Unit Test
unit-test-php:
    stage: commit
    extends: .php_cli
    script:
        - php vendor/bin/phpunit
    rules:
        - if: $CI_PIPELINE_SOURCE == "merge_request_event"
