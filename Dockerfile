FROM registry.gitlab.com/managementdrives/cloud-devops/base-images/public/base-image-frankenphp:8.4

WORKDIR /app

ENV APP_DEBUG=0
ENV APP_ENV=dev

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# prevent the reinstalation of vendors at every changes in the source code
COPY --link --chown=frankenphp:frankenphp composer.* symfony.* ./

RUN set -eux; \
    git config --global --add safe.directory /app; \
	composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress

# Ini prod file
COPY --link docker/frankenphp/conf.d/app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link docker/frankenphp/Caddyfile /etc/caddy/Caddyfile

COPY --link --chown=frankenphp:frankenphp  . /

RUN set -eux; \
	mkdir -p var/cache var/log; \
	composer dump-autoload --classmap-authoritative --no-dev; \
	composer dump-env prod; \
	composer run-script --no-dev post-install-cmd; \
	chmod +x bin/console; \
    sync;

USER ${USER}

CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]
