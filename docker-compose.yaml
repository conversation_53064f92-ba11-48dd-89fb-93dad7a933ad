services:
    chatbot-base-frankenphp:
        build:
            context: .
            dockerfile: ./docker/dev/frankenphp/Dockerfile
        container_name: chatbot-base-frankenphp
        restart: unless-stopped
        environment:
            - SERVER_NAME=chatbot.local
        ports:
            - target: 80
              published: 9080
              protocol: tcp
                # HTTPS
            - target: 443
              published: 9443
              protocol: tcp
        volumes:
            - ./:/app
            - ./docker/dev/Caddyfile:/etc/caddy/Caddyfile:ro
            - ./docker/dev/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
            - caddy_data:/data
            - caddy_config:/config
        networks:
            - chatbot-base
        extra_hosts:
            - "host.docker.internal:host-gateway"
        tty: true
volumes:
    caddy_data:
    caddy_config:

networks:
    chatbot-base:
        driver: bridge
