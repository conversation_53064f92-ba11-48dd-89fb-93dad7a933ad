<?php

declare(strict_types=1);

namespace App\Infrastructure\Response;

class ApiResponseContent
{
    /**
     * @param array<int, mixed>|array<string, mixed>|null $data
     * @param array<string, mixed>                        $metadata
     */
    public function __construct(
        private readonly ?array $data = null,
        private readonly string $message = '',
        private readonly int $errorCode = 0,
        private array $metadata = [],
    ) {
    }

    /**
     * @param array<array>|array<int, mixed>|array<string, mixed> $data
     */
    public static function createFromData(array $data): self
    {
        return new self($data);
    }

    public static function createFromMessage(string $message): self
    {
        return new self(
            message: $message,
            errorCode: 1,
        );
    }

    /**
     * @param array<string, mixed> $metadata
     */
    public function setMetadata(array $metadata): self
    {
        return new self(
            data: $this->data,
            message: $this->message,
            errorCode: $this->errorCode,
            metadata: $metadata,
        );
    }

    /**
     * @param array<string, mixed> $metadata
     */
    public function addMetadata(array $metadata): self
    {
        $this->metadata = array_merge($this->metadata, $metadata);

        return new self(
            data: $this->data,
            message: $this->message,
            errorCode: $this->errorCode,
            metadata: array_merge($this->metadata, $metadata),
        );
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return array_merge(
            null !== $this->data ? ['data' => $this->data] : [],
            empty($this->message) ? [] : ['message' => $this->message],
            0 === $this->errorCode ? [] : ['error' => $this->errorCode],
            empty($this->metadata) ? [] : ['metadata' => $this->metadata],
        );
    }
}
