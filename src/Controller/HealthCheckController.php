<?php

declare(strict_types=1);

namespace App\Controller;

use App\Application\Query\HealthCheckQuery;
use App\Domain\Bus\QueryBusAccessor;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

readonly class HealthCheckController extends QueryBusAccessor
{
    public function __invoke(): Response
    {
        $status = $this->ask(new HealthCheckQuery());

        return new JsonResponse(
            status: $status,
        );
    }
}
